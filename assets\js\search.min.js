$(document).ready(function(){
  // Handle search input
  let searchInput = document.getElementById("searchInput");

  searchInput.addEventListener("input", function() {
    let searchTerm = searchInput.value.toLowerCase().trim();

    // Get all folder segments
    let folderSegments = $("#fileList .ui.segment");

    // If search is empty, show all folders and items
    if (searchTerm === "") {
      folderSegments.show();
      $("#fileList .item").show();
      return;
    }

    // Hide all folder segments initially
    folderSegments.hide();

    // Check each folder segment
    folderSegments.each(function() {
      let folderSegment = $(this);
      let folderLabel = folderSegment.find(".ui.teal.ribbon.label").first();
      let folderName = folderLabel.length ? folderLabel.text().toLowerCase() : "";
      let folderItems = folderSegment.find(".item");
      let hasVisibleItems = false;
      let folderMatches = folderName.includes(searchTerm);

      if (folderMatches) {
        // If folder matches, show all items
        folderItems.show();
        hasVisibleItems = true;
      } else {
        // Check each item in this folder
        folderItems.each(function() {
          let item = $(this);
          let itemText = item.text().toLowerCase();

          if (itemText.includes(searchTerm)) {
            item.show();
            hasVisibleItems = true;
          } else {
            item.hide();
          }
        });
      }

      // Show the folder segment only if it matches or has visible items
      if (folderMatches || hasVisibleItems) {
        folderSegment.show();
      }
    });
  });
});
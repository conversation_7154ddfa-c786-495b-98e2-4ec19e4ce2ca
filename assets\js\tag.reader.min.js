function showTags(fileUrl) {
  // Fetch the file as a Blob and use jsmediatags
  fetch(fileUrl)
    .then(response => response.blob())
    .then(blob => {
      window.jsmediatags.read(blob, {
        onSuccess: function(tag) {
          displayTags(tag.tags);
        },
        onError: function(error) {
          console.error("Error reading ID3 tags:", error);
          displayTags({}); // Clear UI on error
        }
      });
    })
    .catch(e => {
      console.error("Error fetching file for tag reading:", e);
      displayTags({});
    });
}

function displayTags(t) {
  // Set tag values
  var titleEl = document.getElementById("title");
  var artistEl = document.getElementById("artist");
  var albumEl = document.getElementById("album");
  var yearEl = document.getElementById("year");
  var trackEl = document.getElementById("track");

  // Set text content
  titleEl.textContent = t.title || "";
  artistEl.textContent = t.artist || "";
  albumEl.textContent = t.album || "";
  yearEl.textContent = t.year || "";
  trackEl.textContent = t.track || "";

  // Ensure text color is white
  titleEl.style.color = "#ffffff";
  artistEl.style.color = "#ffffff";
  albumEl.style.color = "#ffffff";
  yearEl.style.color = "#ffffff";
  trackEl.style.color = "#ffffff";

  // Handle album art
  var e = t.picture;
  if (e) {
    var base64String = "";
    for (var i = 0; i < e.data.length; i++) {
      base64String += String.fromCharCode(e.data[i]);
    }
    var n = "data:" + e.format + ";base64," + window.btoa(base64String);
    document.getElementById("picture").setAttribute("src", n);
  } else {
    document.getElementById("picture").setAttribute("src", "./assets/images/cover.png");
  }
}
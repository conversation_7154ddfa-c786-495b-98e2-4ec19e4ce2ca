<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CMP Media Player</title>
    <script src="./assets/js/jquery-3.7.1.min.js"></script>
    <script src="./assets/js/search.min.js"></script>
    <script src="./assets/js/tag.reader.min.js"></script>
    <script src="./assets/js/cmp.player.min.js"></script>
    <script src="./assets/js/jsmediatags.min.js"></script>

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/fomantic-ui@2.9.2/dist/semantic.min.css">
    <script src="https://cdn.jsdelivr.net/npm/fomantic-ui@2.9.2/dist/semantic.min.js"></script>

    <style>
        body {
            background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
                url('./assets/images/bg2_up.jpg') no-repeat center/cover fixed;
            min-height: 100vh;
            padding-bottom: 80px;
            animation: fadeIn 1.5s ease-out;
        }

        @keyframes fadeIn {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }

            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Override Fomantic UI styles for dark theme */
        .ui.segment {
            background: rgba(27, 28, 29, 0.8);
            color: #fff;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        .ui.list .item {
            color: #fff;
        }

        .ui.list .header {
            color: #24add6;
        }

        .ui.list .description,
        .description,
        #title,
        #artist,
        #album,
        #year,
        #track {
            color: #fff !important;
            margin-top: 0.5rem;
            font-size: 1.1em;
            font-weight: 500;
        }

        .ui.list .item {
            padding: 0.8em 0;
        }

        .ui.horizontal.label {
            min-width: 80px;
            text-align: center;
        }

        .ui.divider {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        #playlist-container {
            max-height: 550px;
            overflow-y: auto;
        }

        /* White scrollbar for playlist */
        #playlist-container::-webkit-scrollbar {
            width: 10px;
            background: transparent;
        }

        #playlist-container::-webkit-scrollbar-thumb {
            background: #ff0000;
            border-radius: 5px;
        }

        #playlist-container::-webkit-scrollbar-track {
            background: transparent;
        }

        /* Firefox */
        #playlist-container {
            scrollbar-width: thin;
            scrollbar-color: #792626 transparent;
        }

        /* Custom styles for the player */
        .playing {
            background: linear-gradient(90deg, rgba(36, 173, 214, 0.2), rgba(36, 173, 214, 0.1));
            color: #fff;
            font-weight: bold;
            border-left: 3px solid #24add6;
        }

        .completed {
            opacity: 0.6;
            font-style: italic;
        }

        .build-time-message {
            color: #24add6;
            text-align: center;
            margin: 1rem 0;
            font-weight: 500;
        }

        /* Responsive adjustments */
        @media (max-width: 767px) {
            .ui.grid .column {
                width: 100% !important;
                margin-bottom: 1rem;
            }
        }

        .ui.relaxed.divided.list .item:not(.playing):not(.completed):hover {
            background: linear-gradient(90deg, rgba(36, 173, 214, 0.12), rgba(36, 173, 214, 0.05));
            color: #24add6;
            cursor: pointer;
            transition: background 0.2s, color 0.2s;
        }
    </style>

</head>

<body>
    <div class="ui container">
        <h1 class="ui center aligned header" style="margin-top: 2rem; color: #24add6;">
            <i class="music icon"></i>
            <div class="content">Playlist</div>
        </h1>

        <div class="ui fluid action input" style="margin: 2rem 0;">
            <input type="text" id="searchInput" placeholder="Search songs...">
            <button id="rebuildPlaylistBtn" class="ui teal right labeled icon button">
                <i class="sync icon"></i>
                Rebuild Playlist
            </button>
        </div>

        <div class="ui grid">
            <div class="six wide column">
                <div class="ui segment" id="id3-details">
                    <h3 class="ui center aligned header" style="color: #24add6; margin-bottom: 1.5rem;">
                        <i class="info circle icon"></i>
                        <div class="content">Song Details</div>
                    </h3>
                    <div class="ui divided list">
                        <div class="item">
                            <div class="ui teal horizontal label">Title</div>
                            <div id="title" class="description"></div>
                        </div>
                        <div class="item">
                            <div class="ui teal horizontal label">Artist</div>
                            <div id="artist" class="description"></div>
                        </div>
                        <div class="item">
                            <div class="ui teal horizontal label">Album</div>
                            <div id="album" class="description"></div>
                        </div>
                        <div class="item">
                            <div class="ui teal horizontal label">Year</div>
                            <div id="year" class="description"></div>
                        </div>
                        <div class="item">
                            <div class="ui teal horizontal label">Track</div>
                            <div id="track" class="description"></div>
                        </div>
                    </div>
                    <div class="ui divider"></div>
                    <div class="ui centered image">
                        <img id="picture" class="ui rounded image" src="./assets/images/cover.png" width="200px"
                            alt="Album cover" />
                    </div>
                    <canvas id="audioVisualizer" width="200" height="200" style="margin-top: 1rem;"></canvas>
                </div>
            </div>

            <div class="ten wide column">
                <div class="ui segment" id="playlist-container">
                    <h3 class="ui center aligned header" style="color: #24add6; margin-bottom: 1.5rem;">
                        <i class="list icon"></i>
                        <div class="content">Music Library</div>
                    </h3>
                    <div class="ui relaxed divided list" id="fileList"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="ui bottom fixed menu" style="background: transparent; border: none; box-shadow: none; padding: 1rem;">
        <div class="ui container">
            <audio id="mainAudioPlayer" controls class="ui fluid" style="width: 100%;"></audio>
        </div>
    </div>

    <script>
        // Get the canvas element and its context
        var canvas = document.getElementById('audioVisualizer');
        var ctx = canvas.getContext('2d');

        // --- FIX: Only create AudioContext and MediaElementSource once ---
        var audioContext = null;
        var audioSrc = null;
        var analyser = null;
        var drawAnimationId = null;

        function visualizeAudio() {
            var audioElement = document.getElementById('mainAudioPlayer');

            // Only create context and source once
            if (!audioContext) {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                audioSrc = audioContext.createMediaElementSource(audioElement);
                analyser = audioContext.createAnalyser();
                analyser.fftSize = 256;
                audioSrc.connect(analyser);
                audioSrc.connect(audioContext.destination);
            }

            var bufferLength = analyser.frequencyBinCount;
            var dataArray = new Uint8Array(bufferLength);

            // Cancel previous animation if any
            if (drawAnimationId) {
                cancelAnimationFrame(drawAnimationId);
            }

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                analyser.getByteFrequencyData(dataArray);
                var barWidth = canvas.width / bufferLength;
                var barHeight;
                var x = 0;
                for (var i = 0; i < bufferLength; i++) {
                    barHeight = dataArray[i] / 2;
                    ctx.fillStyle = 'rgb(' + (barHeight + 100) + ',50,50)';
                    ctx.fillRect(x, canvas.height - barHeight, barWidth, barHeight);
                    x += barWidth + 1;
                }
                drawAnimationId = requestAnimationFrame(draw);
            }
            draw();
        }

        // Call the visualizeAudio function when the audio starts playing
        document.getElementById('mainAudioPlayer').addEventListener('play', visualizeAudio);

    </script>

</body>

</html>
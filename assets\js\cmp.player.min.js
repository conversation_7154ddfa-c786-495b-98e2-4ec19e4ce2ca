function retrieveFileList(e, i, callback) {
  var a = new Date().getTime();
  showMessage("Building playlist...");
  $.ajax({
    url: "get_file_list.php",
    type: "POST",
    data: { folderPath: e },
    dataType: "json",
    success: function (e) {
      var t = new Date().getTime();
      hideMessage();
      var n = {};
      $.each(e, function (e, a) {
        if (!isExcludedFolder(a.path, i)) {
          var t = a.path.substring(0, a.path.lastIndexOf("/"));
          n[t] || (n[t] = []), n[t].push(a)
        }
      });
      displayBuildTime((t - a) / 1e3);
      var s = $("#fileList");
      s.empty();
      $.each(n, function (e, i) {
        i.sort(function (e, i) {
          var a = e.name.toLowerCase(), t = i.name.toLowerCase();
          return a < t ? -1 : a > t ? 1 : 0
        });

        // Create a header for each folder
        var folderHeader = $("<div>").addClass("ui teal ribbon label").text(e);
        var folderSection = $("<div>").addClass("ui segment").append(folderHeader);

        // Create a list for the files in this folder
        var filesList = $("<div>").addClass("ui relaxed divided list");

        $.each(i, function (e, i) {
          var fileItem = $("<div>").addClass("item");
          var fileContent = $("<div>").addClass("content");
          var fileLink = $("<a>").attr("href", i.path).addClass("header").text(i.name);
          fileLink.css("color", "#24add6");

          fileContent.append(fileLink);
          fileItem.append(fileContent);
          filesList.append(fileItem);
        });

        folderSection.append(filesList);
        s.append(folderSection);
      });

      // Call the callback function if provided
      if (typeof callback === 'function') {
        callback();
      }
    },
    error: function(xhr, status, error) {
      console.error("Error building playlist:", error);
      hideMessage();
      showMessage("Error building playlist. Please try again.", true);
      if (typeof callback === 'function') {
        callback(error);
      }
    }
  });
}

function displayBuildTime(e) {
  var i = "Playlist built in " + e.toFixed(2) + " seconds";
  $(".build-time-message").remove();
  var message = $("<div>").addClass("ui info message build-time-message").text(i);
  $("#playlist-container").prepend(message);
}

function retrieveExcludedFolders(e) {
  $.getJSON("config.json").done(function (i) {
    i && i.excluded_folders ? e(i.excluded_folders) : (console.error("Invalid or missing excluded folders in the configuration file."), e([]))
  }).fail(function (i, a, t) {
    console.error("Failed to load configuration file: " + a + ", " + t), e([])
  })
}

function showMessage(e, i) {
  hideMessage();
  var message = $("<div>").addClass("ui floating teal message").text(e);
  if (i) {
    message.addClass("persistent");
  }
  $("body").prepend(message);

  if (!i) {
    setTimeout(function () { hideMessage() }, 5000);
  }
}

function hideMessage() {
  $(".ui.message:not(.build-time-message, .persistent)").remove();
}

function isExcludedFolder(e, i) {
  for (var a = 0; a < i.length; a++) {
    if (-1 !== e.indexOf(i[a])) return !0;
  }
  return !1;
}

function rebuildPlaylist() {
  retrieveExcludedFolders(function (e) {
    // Pass a callback function to retrieveFileList to load the playlist after file_list.json is updated
    retrieveFileList("../music/", e, function(error) {
      if (!error) {
        // Load the playlist from the updated file_list.json
        loadPlaylistFromJSON();
      }
    });
  })
}

// Function to load playlist from file_list.json
function loadPlaylistFromJSON() {
  // Add cache-busting parameter to force a fresh load
  $.getJSON("file_list.json?_=" + new Date().getTime(), function(data) {
    var folders = {};
    $.each(data, function(i, item) {
      if (item.path) {
        var folderPath = item.path.substring(0, item.path.lastIndexOf("/"));
        folders[folderPath] || (folders[folderPath] = []);
        folders[folderPath].push(item);
      }
    });

    var fileList = $("#fileList");
    fileList.empty(); // Clear the current playlist

    $.each(folders, function(folder, files) {
      files.sort(function(a, b) {
        var nameA = a.name.toLowerCase(), nameB = b.name.toLowerCase();
        return nameA < nameB ? -1 : nameA > nameB ? 1 : 0;
      });

      // Create a header for each folder
      var folderHeader = $("<div>").addClass("ui teal ribbon label").text(folder);
      var folderSection = $("<div>").addClass("ui segment").append(folderHeader);

      // Create a list for the files in this folder
      var filesList = $("<div>").addClass("ui relaxed divided list");

      $.each(files, function(i, file) {
        var fileItem = $("<div>").addClass("item");
        var fileContent = $("<div>").addClass("content");
        var fileLink = $("<a>").attr("href", file.path).addClass("header").text(file.name);
        fileLink.css("color", "#24add6");

        fileContent.append(fileLink);
        fileItem.append(fileContent);
        filesList.append(fileItem);
      });

      folderSection.append(filesList);
      fileList.append(folderSection);
    });
  }).fail(function() {
    showMessage("Error loading playlist. Please try again.", true);
  });
}

$(document).ready(function () {
  var currentlyPlaying = null; // Track the currently playing file

  // Initialize Fomantic UI components
  $('.ui.dropdown').dropdown();

  // Load the playlist from file_list.json
  loadPlaylistFromJSON();

  // Handle rebuild button click
  $("#rebuildPlaylistBtn").on("click", function() {
    var $button = $(this);
    $button.addClass("loading");

    // Clear search input when rebuilding
    $("#searchInput").val("");

    // Override the loadPlaylistFromJSON function temporarily to handle button state
    var originalLoadPlaylist = loadPlaylistFromJSON;
    loadPlaylistFromJSON = function() {
      // Call the original function
      originalLoadPlaylist();

      // Remove loading state and trigger event
      $button.removeClass("loading");
      $(document).trigger("playlistRebuilt");

      // Restore the original function
      loadPlaylistFromJSON = originalLoadPlaylist;
    };

    // Start the rebuild process
    rebuildPlaylist();
  });

  // Handle song click
  $("#fileList").on("click", "a", function(i) {
    i.preventDefault();
    var a = $(this).attr("href");
    $("#mainAudioPlayer").attr("src", a);
    $("#mainAudioPlayer")[0].play();
    showTags(a);

    // Update UI to show playing state
    $(".item").removeClass("playing");
    $(this).closest(".item").addClass("playing");
    currentlyPlaying = $(this);
  });

  // Handle song ended
  $("#mainAudioPlayer").on("ended", function() {
    var currentItem = $(".playing");
    var nextItem = currentItem.closest(".item").next(".item");
    // Mark the current as completed and remove playing
    currentItem.addClass("completed").removeClass("playing");
    // If there is a next song, play it
    if (nextItem.length > 0) {
      nextItem.find("a").trigger("click");
    }
  });

  // Set initial volume
  document.getElementById("mainAudioPlayer").volume = 0.2;
});
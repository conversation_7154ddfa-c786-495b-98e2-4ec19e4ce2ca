<?php
$folderPath = $_POST['folderPath'];

$fileList = array();

$allowedExtensions = array('mp3', 'm4a');

$excludedFolders = []; // Initialize an array to hold excluded folders

// Load excluded folders from configuration file
$configData = json_decode(file_get_contents('config.json'), true);
if ($configData && isset($configData['excluded_folders'])) {
    $excludedFolders = $configData['excluded_folders'];
}

$iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($folderPath), RecursiveIteratorIterator::SELF_FIRST);
foreach ($iterator as $file) {
    if ($file->isFile() && in_array($file->getExtension(), $allowedExtensions)) {
        // Check if the file's directory is excluded
        $filePath = $file->getPathname();
        $relativePath = substr($filePath, strlen($folderPath) + 1);
        $relativePathParts = explode('/', $relativePath);
        $folderToCheck = $relativePathParts[0];

        // Debug logging
        error_log("Checking folder: '" . $folderToCheck . "' against excluded: " . json_encode($excludedFolders));

        if (!in_array($folderToCheck, $excludedFolders)) {
            $artist = $relativePathParts[0] ?? '';
            $album = $relativePathParts[1] ?? '';

            $fileList[] = array(
                'name' => $file->getFilename(),
                'path' => $filePath,
            );
        }
    }
}

// Save the file list to a JSON file
$jsonFilePath = 'file_list.json';
file_put_contents($jsonFilePath, json_encode($fileList, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_NUMERIC_CHECK));

echo json_encode($fileList);
?>

